import os
import uuid
from datetime import datetime
from tempfile import SpooledTemporaryFile
from typing import Type
from unittest.mock import MagicMock
from uuid import uuid4

from pydantic.dataclasses import dataclass

from api.sim.examples import PROVIDER_LOG
from audit.domain.model import Providerlog
from auth.exceptions import NotFound
from cdrdata.adapters.repository import AbstractCdrRepository, InMemoryCdrRepository
from common.parser import BaseCSVParser
from common.searching import Search, Searching
from common.types import ICCID, IMSI, MSISDN, FormFactor, Month, SimStatus
from common.utils import trace_id_var
from rate_plans.adapters.rate_plan_repository import (
    AbstractRatePlanRepository,
    InMemoryRatePlanRepository,
)
from rate_plans.domain.model import RatePlan
from redis.adapters.externalapi import HTTPRedisAPI
from sim import exceptions
from sim.adapters.externalapi import (
    AbstractAuditService,
    AbstractMarketShareAPI,
    AbstractSIMProvisioningAPI,
    FakeMarketShareAPI,
    FakeSIMProvisioningAPI,
)
from sim.adapters.orm import model
from sim.adapters.repository import AbstractSimRepository, InMemorySimRepository
from sim.domain import model as domain_model
from sim.domain.model import NotificationStatus, RequestType, SIMProviderLog
from sim.domain.model import SimStatus as sim_status
from sim.notification_service import SimNotification
from sim.parser import SIMCardCSVParser
from sim.services import MediaService, SimService

import pytest  # isort: skip


START_WITH = 0
END_WITH = 3


@dataclass
class ProviderLog:
    imsi: IMSI
    requestType: str
    priorStatus = SimStatus.ACTIVE


@dataclass
class AuditResult:
    results: list[model.AuditLogs]
    totalCount: int


class SimContract:
    @pytest.fixture
    def sim_repository(self, *args, **kwargs) -> AbstractSimRepository:
        raise NotImplementedError()

    @pytest.fixture
    def rate_plan_repository(self, *args, **kwargs) -> AbstractRatePlanRepository:
        raise NotImplementedError()

    @pytest.fixture
    def provisioning(self, *args, **kwargs) -> AbstractSIMProvisioningAPI:
        raise NotImplementedError()

    @pytest.fixture
    def cdr_repository(self, *args, **kwargs) -> AbstractCdrRepository:
        raise NotImplementedError()

    @pytest.fixture
    def market_share_api(self, *args, **kwargs) -> AbstractMarketShareAPI:
        raise NotImplementedError()

    @pytest.fixture
    def sim_service(
        self,
        sim_repository: AbstractSimRepository,
        rate_plan_repository: AbstractRatePlanRepository,
        provisioning: AbstractSIMProvisioningAPI,
        cdr_repository: AbstractCdrRepository,
        market_share_api: AbstractMarketShareAPI,
        audit_service: AbstractAuditService,
        media_service: MediaService,
        redis_api: HTTPRedisAPI,
    ) -> SimService:
        return SimService(
            sim_repository=sim_repository,
            rate_plan_repository=rate_plan_repository,
            provisioning=provisioning,
            cdr_repository=cdr_repository,
            market_share_api=market_share_api,
            audit_service=audit_service,
            media_service=media_service,
            redis_api=redis_api,
        )

    @pytest.fixture
    def build_searching(self):
        def factory(search: str, fields: set[str]):
            return Searching(search=Search(search), fields=fields)

        return factory

    @pytest.fixture
    def make_sim_cards(self):
        def factory(start_with=0, end_with=10):
            return [
                model.SIMCard(
                    iccid=f"75647583748396758{str(i).zfill(3)}",
                    imsi=f"************{str(i).zfill(3)}",
                    msisdn=f"*********{str(i).zfill(3)}",
                    rate_plan_id=None,
                    sim_status=SimStatus.DEACTIVATED,
                )
                for i in range(start_with, end_with)
            ]

        return factory

    @pytest.fixture
    def make_allocation(self):
        def factory(range_, rate_plan_id=1):
            return model.Allocation(
                id=range_.id,
                title="Reference",
                account_id=1,
                range_id=range_.id,
                quantity=range_.quantity,
                imsi_first=range_.imsi_first,
                imsi_last=range_.imsi_last,
                rate_plan_id=rate_plan_id,
            )

        return factory

    @pytest.fixture
    def create_rate_plan(self, rate_plan_repository):
        def factory(**kwargs):
            _kwargs = {"account_id": 1, "name": "test", "access_fee": 0.2, **kwargs}
            rate_plan = RatePlan(**_kwargs)
            return rate_plan_repository.add(rate_plan)

        return factory

    @pytest.fixture
    def ranges(self, make_sim_cards):
        start_with = 0
        end_with = 3
        ranges = []
        for i in range(0, 3):
            sim_cards = make_sim_cards(start_with=start_with, end_with=end_with)
            range_ = model.Range(
                title=f"test-{i}",
                form_factor="MICRO",
                created_by="Brooklyn Simmons",
                imsi_first=sim_cards[0].imsi,
                imsi_last=sim_cards[-1].imsi,
                remaining=end_with - start_with,
                quantity=end_with - start_with,
            )
            range_.sim_cards = sim_cards
            ranges.append(range_)
            start_with += end_with
            end_with += end_with

        return ranges

    @pytest.fixture
    def make_cards_statistic(self, make_sim_cards):
        start_with = 0
        end_with = 3
        cards_active_statistic = []
        for i in range(0, 3):
            sim_cards = make_sim_cards(start_with=start_with, end_with=end_with)
            make_cards_ = {
                "id": i + 1,
                "iccid": sim_cards[i].iccid,
                "imsi": sim_cards[i].imsi,
                "msisdn": sim_cards[i].msisdn,
                "sim_status": SimStatus.ACTIVE.name,
                "is_first_activation": True,
                "usage": 1245,
                "rate_plan_id": 1,
                "account_id": i + 1,
                "month": Month(2023, 5, 1),
            }
            cards_active_statistic.append(make_cards_)
        return cards_active_statistic

    @pytest.fixture
    def sim_summary(self):
        return {
            "**********12345": {
                "msisdn": "**********",
                "imsi": "**********12345",
                "iccid": "******************9",
                "sim_status": "Active",
                "rate_plan_id": 1,
                "rate_plan": "PAYG",
            }
        }

    @pytest.fixture
    def push_notification_service(
        self,
        sim_repository: AbstractSimRepository,
        rate_plan_repository: AbstractRatePlanRepository,
        provisioning: AbstractSIMProvisioningAPI,
        cdr_repository: AbstractCdrRepository,
        market_share_api: AbstractMarketShareAPI,
        audit_service: AbstractAuditService,
    ) -> SimService:
        return SimService(  # type: ignore
            sim_repository=sim_repository,
            rate_plan_repository=rate_plan_repository,
            provisioning=provisioning,
            cdr_repository=cdr_repository,
            market_share_api=market_share_api,
            audit_service=audit_service,
        )

    @pytest.fixture
    def make_sim_provider_log(self):
        def factory(work_id, status, audit_date):
            return SIMProviderLog(
                sim_activity_log_uuid="2123455",
                activity_id="ea4c11bc-afca-4d91-be8b-a77e4829964a",
                message="Completed",
                prior_status="Active",
                work_id=work_id,
                status=status,
                audit_date=audit_date,
            )

        return factory

    @pytest.fixture
    def make_sim_card(self):
        def factory(start_with=0, end_with=10):
            return [
                model.SIMCard(
                    id=i + 1,
                    iccid=f"75647583748396758{str(i).zfill(3)}",
                    imsi=f"************{str(i).zfill(3)}",
                    msisdn=f"*********{str(i).zfill(3)}",
                    rate_plan_id=None,
                )
                for i in range(start_with, end_with)
            ]

        return factory

    def test_get_sim_card(self, make_sim_card):
        push_notification_service = InMemorySimRepository()
        response = push_notification_service.get_sim_cards()
        assert response is not None

    def test_get_sim_monthly_status(
        self,
        make_sim_card,
        make_cards_statistic,
    ):
        push_notification_service = InMemorySimRepository()
        for i in range(0, 3):
            sim_card_id = push_notification_service.add_sim_monthly_status(
                make_cards_statistic[i]
            )
            response = push_notification_service.check_sim_monthly_status(
                sim_card_id, make_cards_statistic[i]["month"]
            )
            assert response is True

    def test_add_sim_monthly_status(
        self,
        make_sim_card,
        make_cards_statistic,
    ):
        push_notification_service = InMemorySimRepository()
        for i in range(0, 3):
            sim_cards = make_sim_card(start_with=START_WITH, end_with=END_WITH)
            assert (
                push_notification_service.add_sim_monthly_status(
                    make_cards_statistic[i]
                )
                == sim_cards[i].id
            )

    def test_update_sim_monthly_status(
        self,
        make_sim_card,
        make_cards_statistic,
    ):
        push_notification_service = InMemorySimRepository()
        for i in range(0, 3):
            sim_card_id = push_notification_service.add_sim_monthly_status(
                make_cards_statistic[i]
            )
            assert (
                push_notification_service.update_sim_monthly_status(
                    sim_card_id,
                    make_cards_statistic[i]["month"],
                    make_cards_statistic[i]["sim_status"],
                )
                is None
            )

    def test_get_connection_summary(self, sim_summary):
        repository = InMemorySimRepository()
        repository.add_sim_summary(sim_summary)
        summary = repository.get_connection_summary("**********12345")

        expected_summary = model.ConnectionSummary(
            msisdn="**********",
            imsi="**********12345",
            iccid="******************9",
            first_activated=None,
            last_session=None,
            sim_status=SimStatus.ACTIVE,
            rate_plan_id=1,
            rate_plan="PAYG",
        )
        assert summary == expected_summary

    @pytest.fixture
    def generate_sim_management(self):
        sim_data = []
        for i in range(0, 3):
            data = {
                "sim_id": 1 + 1,
                "account_id": i + 1,
                "iccid": str(******************9 + i),
                "msisdn": str(********** + i),
                "imsi": str(**********12345 + i),
                "type": "NANO" if i % 2 == 0 else "MICRO",
                "allocation_reference": f"reference{i+1}",
                "allocation_date": datetime.now(),
                "sim_status": SimStatus.ACTIVE if i % 2 == 0 else SimStatus.DEACTIVATED,
                "usage": 100 if i % 2 == 0 else 50,
                "rate_plan": "PAYG",
                "ee_usage": 100 + i,
                "sim_profile": model.SimProfile.DATA_ONLY,
                "msisdn_factor": model.MSISDNFactor.NATIONAL,
            }
            sim_data.append(data)
        return sim_data

    @pytest.fixture
    def sim_status_data(self):
        return {
            "imsi": "**********12345",
            "iccid": "******************9",
            "msisdn": "**********",
            "sim_status": model.SimStatus.PENDING,
        }

    def test_sim_status_details(self, sim_status_data):
        repository = InMemorySimRepository()
        repository.add_sim_status_data(sim_status_data)
        pending_sims = repository.sim_status_details()
        expected_data = model.SimStatusDetails(
            msisdn="**********",
            imsi="**********12345",
            iccid="******************9",
            sim_status=model.SimStatus.PENDING,
        )
        assert expected_data == pending_sims

    def test_get_sim_usage(self, generate_sim_management):
        repository = InMemorySimRepository()
        repository.add_sim_card_data(generate_sim_management)
        assert repository.get_sim_usage(1, None) is not None
        for i in repository.get_sim_usage(1, None):
            assert i.iccid == "******************9"
            assert i.imsi == "**********12345"
            assert i.msisdn == "**********"
            assert i.sim_status == SimStatus.ACTIVE
            assert i.usage == 100

    def test_get_sim_usage_count(self, generate_sim_management):
        repository = InMemorySimRepository()
        repository.add_sim_card_data(generate_sim_management)
        assert repository.get_sim_usage_count(1) == (10, 2, 2, 2, 4)

    def test_card_active_statistic(
        self,
        make_cards_statistic,
    ):
        repository = InMemorySimRepository()
        repository.add_card_active_statistic(make_cards_statistic)
        assert repository.cards_active_statistic(1, Month(2023, 5, 1), None) is not None

    def test_update_sim_status_by_imsi(self, make_sim_cards):
        repository = InMemorySimRepository()
        for i in range(0, 3):
            sim_cards = make_sim_cards(start_with=0, end_with=3)
            repository.add_sim_cards(
                sim_cards[i].imsi, sim_cards[i].msisdn, sim_cards[i].sim_status
            )
            assert (
                repository.update_sim_status_by_imsi(
                    sim_cards[i].imsi, sim_status="Active"
                )
                is None
            )

    def test_add_bulk_sim_monthly_statistics(
        self,
        make_cards_statistic,
    ):
        repository = InMemorySimRepository()
        for i in range(0, 3):
            sim_card_statistics = []
            sim_card_statistics.append(make_cards_statistic[i])
            assert repository.add_bulk_sim_monthly_statistics(
                sim_card_statistics
            ) == len(sim_card_statistics)

    def test_copy_monthly_statistics(
        self,
    ):
        today = datetime.today()
        year = today.year
        month = today.month
        current_month = Month(year=year, month=month, day=1)
        expected_result = model.SIMMonthlyStatus(
            sim_card_id=1,
            month=current_month,
            sim_status=sim_status.ACTIVE,
            is_first_activation=True,
        )
        repository = InMemorySimRepository()
        for _ in range(0, 3):
            copied_monthly_statistics_data = list(repository.copy_monthly_statistics())
            assert (
                copied_monthly_statistics_data[0].sim_card_id
                == expected_result.sim_card_id
            )
            assert copied_monthly_statistics_data[0].month == expected_result.month
            assert (
                copied_monthly_statistics_data[0].sim_status
                == expected_result.sim_status
            )
            assert (
                copied_monthly_statistics_data[0].is_first_activation
                == expected_result.is_first_activation
            )


class TestSimService(SimContract):
    @pytest.fixture
    def sim_repository(self) -> InMemorySimRepository:
        return InMemorySimRepository()

    @pytest.fixture
    def rate_plan_repository(self) -> InMemoryRatePlanRepository:
        return InMemoryRatePlanRepository(rate_plans=[])

    @pytest.fixture
    def provisioning(self) -> FakeSIMProvisioningAPI:
        return FakeSIMProvisioningAPI()

    @pytest.fixture
    def cdr_repository(self) -> InMemoryCdrRepository:
        return InMemoryCdrRepository()

    @pytest.fixture
    def market_share_api(self) -> FakeMarketShareAPI:
        return FakeMarketShareAPI()


class TestServiceSim:
    def setup_method(self):
        self.simcard = SimService(
            AbstractSimRepository,
            AbstractRatePlanRepository,
            AbstractSIMProvisioningAPI,
            AbstractCdrRepository,
            AbstractMarketShareAPI,
            AbstractAuditService,
            MediaService,
            HTTPRedisAPI,
        )

    def read_file(self, file_path) -> SpooledTemporaryFile:
        with open(file_path, "rb") as f:
            file_data = f.read()
        spooled_tempfile = SpooledTemporaryFile(mode="w+b")
        spooled_tempfile.write(file_data)
        spooled_tempfile.seek(0)
        return spooled_tempfile

    def test_create_range_when_imsi_exits(self):
        title = "Reference"
        form_factor = FormFactor.STANDARD
        created_by = "John Billing"
        file_path = os.getcwd() + "/tests/data/RANGE_IMSI.csv"
        parser_impl: Type[BaseCSVParser] = SIMCardCSVParser
        spooled_tempfile = self.read_file(file_path)
        self.simcard.sim_repository.check_imsi_range = MagicMock(return_value=1)
        message = (
            "SIM cards already exist for range from 234588570010011 to 234588570010011."
        )
        client_ip = "127.0.0.1"
        with pytest.raises(exceptions.SimCardImsiAlreadyExist) as e:
            self.simcard.create_range(
                title,
                form_factor,
                created_by,
                spooled_tempfile,
                client_ip,
                parser_impl,
            )
        assert e.value.args[0].strip(".") == message.strip(".")

    def test_create_range(self):
        """Need to check this testcase"""
        title = "Reference"
        form_factor = FormFactor.STANDARD
        created_by = "John Billing"
        file_path = os.getcwd() + "/tests/data/RANGE_IMSI.csv"
        parser_impl: Type[BaseCSVParser] = SIMCardCSVParser
        spooled_tempfile = self.read_file(file_path)
        self.simcard.sim_repository.check_imsi_range = MagicMock(return_value=None)
        self.simcard.sim_repository.add_range = MagicMock(return_value=None)
        self.simcard.sim_repository.get_msisdn_in_sim_card_table = MagicMock(
            return_value=[]
        )
        self.simcard.audit_service.add_upload_msisdn_audit_api = MagicMock(
            return_value={
                "id": [
                    "67c7fc9bb9a8a05ef8baac0c",
                    "67c7fc9bb9a8a05ef8baac0d",
                    "67c7fc9bb9a8a05ef8baac0e",
                    "67c7fc9bb9a8a05ef8baac0f",
                    "67c7fc9bb9a8a05ef8baac10",
                ]
            }
        )
        self.simcard.sim_repository.get_msisdn_in_pool_msisdn_table = MagicMock(
            return_value=[]
        )
        client_ip = "127.0.0.1"
        self.simcard.create_range(
            title,
            form_factor,
            created_by,
            spooled_tempfile,
            client_ip,
            parser_impl,
        )
        self.simcard.sim_repository.add_range.assert_called_once()

    def test_sim_status(self, make_sim_card_model, make_pip_sim_status_response):
        client_ip = "127.0.0.1"
        pip_response_expected = make_pip_sim_status_response(
            sim_status=sim_status.ACTIVE
        )
        self.simcard.provisioning.sim_status = MagicMock(
            return_value=pip_response_expected
        )
        sim_card = make_sim_card_model(sim_status=sim_status.ACTIVE)
        self.simcard.get_sim_card = MagicMock(return_value=sim_card)
        result = self.simcard.sim_status(pip_response_expected.imsi, client_ip)
        assert result == pip_response_expected

    def test_sim_status_when_pip_response_status_not_same_with_sim_status(
        self, make_sim_card_model, make_pip_sim_status_response
    ):
        client_ip = "127.0.0.1"
        pip_response_expected = make_pip_sim_status_response(
            sim_status=sim_status.DEACTIVATED
        )
        sim_card = make_sim_card_model(sim_status=sim_status.ACTIVE)
        self.simcard.get_sim_card = MagicMock(return_value=sim_card)
        self.simcard.provisioning.sim_status = MagicMock(
            return_value=pip_response_expected
        )
        self.simcard.create_sim_activity_log = MagicMock(
            return_value=uuid.UUID("780ba803-5117-45ce-8272-784f18e894f4")
        )
        self.simcard.audit_service.add_sim_audit_api = MagicMock(
            return_value={"id": "89496851651653"}
        )
        self.simcard.sim_repository.update_sim_card = MagicMock(return_value=None)
        self.simcard.sim_repository.get_sim_cards = MagicMock(side_effect=None)
        self.simcard._add_sim_monthly_status = MagicMock(return_value=None)
        result = self.simcard.sim_status(pip_response_expected.imsi, client_ip)
        self.simcard.audit_service.add_sim_audit_api.assert_called_once()
        self.simcard.sim_repository.update_sim_card.assert_called_once()
        self.simcard._add_sim_monthly_status.assert_called_once()
        assert result == pip_response_expected

    def test_sim_status_when_sim_status_RFA(
        self, make_pip_sim_status_response, make_sim_card_model, make_monthly_sim_status
    ):
        """when sim status is READY_FOR_ACTIVATION"""
        client_ip = "127.0.0.1"
        pip_response_expected = make_pip_sim_status_response(
            sim_status=sim_status.DEACTIVATED
        )
        sim_card = make_sim_card_model(sim_status=sim_status.READY_FOR_ACTIVATION)
        self.simcard.get_sim_card = MagicMock(return_value=sim_card)
        self.simcard.provisioning.sim_status = MagicMock(
            return_value=pip_response_expected
        )
        self.simcard.create_sim_activity_log = MagicMock(
            return_value=uuid.UUID("780ba803-5117-45ce-8272-784f18e894f4")
        )

        self.simcard.audit_service.sim_provider_log = MagicMock(
            return_value="11111111111"
        )
        self.simcard.sim_repository.update_sim_card = MagicMock(return_value=None)
        self.simcard.sim_repository.get_sim_cards = MagicMock(side_effect=None)
        self.simcard._get_sim_monthly_status = MagicMock(
            return_value=make_monthly_sim_status
        )
        self.simcard._add_sim_monthly_status = MagicMock(return_value=None)
        result = self.simcard.sim_status(pip_response_expected.imsi, client_ip)
        # Change - if pip response is Deactivated and current sim_card is RFA.
        # Don't update the sim card Status
        self.simcard.sim_repository.update_sim_card.assert_not_called()

        self.simcard.audit_service.sim_provider_log.assert_not_called()
        self.simcard._add_sim_monthly_status.assert_not_called()
        assert result == pip_response_expected

    def test_remove_range_when_range_not_exists(self):
        range_id = 1
        message = "Range with id:1 does not exist."
        self.simcard.sim_repository.get_range_by_id = MagicMock(return_value=None)
        with pytest.raises(exceptions.RangeDoesNotExist) as e:
            self.simcard.remove_range(range_id)
        assert str(e.value) == message

    def test_remove_range(self, make_range):
        range_id = 1
        range = make_range()
        self.simcard.sim_repository.get_range_by_id = MagicMock(return_value=range)
        self.simcard.sim_repository.remove_range = MagicMock(return_value=None)
        self.simcard.remove_range(range_id)
        self.simcard.sim_repository.remove_range.assert_called_once()

    def test_create_empty_range(self, make_range):
        range = make_range()
        self.simcard.sim_repository.add_range = MagicMock(return_value=None)
        result = self.simcard.create_empty_range(
            range.title, range.form_factor, range.created_by
        )
        self.simcard.sim_repository.add_range.assert_called_once()
        assert result.created_by == range.created_by
        assert result.form_factor == range.form_factor
        assert result.title == range.title

    def test_remove_allocations_by_range_id(self):
        range_id = 1
        self.simcard.sim_repository.get_range_by_id = MagicMock(return_value=range_id)
        self.simcard.sim_repository.remove_allocations_in_range = MagicMock(
            side_effect=None
        )
        self.simcard.remove_allocations_by_range_id(range_id)
        self.simcard.sim_repository.remove_allocations_in_range.assert_called_once()

    def test_remove_allocations_by_range_id_when_range_not_exist(self):
        range_id = 1
        expected_message = f"Range with id:{range_id} does not exist."
        self.simcard.sim_repository.get_range_by_id = MagicMock(return_value=None)
        with pytest.raises(exceptions.RangeDoesNotExist) as e:
            self.simcard.remove_allocations_by_range_id(range_id)
        assert str(e.value) == expected_message

    def test_remove_allocation_when_allocation_not_exist(self):
        allocation_id = 1
        expected_message = f"Allocation with id:{allocation_id} does not exist."

        self.simcard.sim_repository.get_allocation_by_id = MagicMock(return_value=None)
        with pytest.raises(exceptions.AllocationDoesNotExist) as e:
            self.simcard.remove_allocation(allocation_id)

        assert str(e.value) == expected_message

    @pytest.mark.skip(reason="I need more time for this test")
    def test_get_sim_remains(self):
        self.simcard.get_sim_remains()

    def test_get_sim_card_when_sim_not_exist_stop_iteration(self, make_sim_card_models):
        """To catch StopIteration exception"""
        imsi = "000496758000001"
        sim_models = make_sim_card_models(start_with=0, end_with=0)
        self.simcard.sim_repository.get_sim_cards = MagicMock(side_effect=sim_models)
        with pytest.raises(StopIteration):
            self.simcard.get_sim_card(imsi)

    def test_get_sim_card_when_sim_not_found(self, make_sim_card_models):
        imsi = "000496758000001"
        expected_message = "Requested IMSI not found."
        sim_models = make_sim_card_models(start_with=0, end_with=0)
        self.simcard.sim_repository.get_sim_cards = MagicMock(
            return_value=iter(sim_models)
        )
        with pytest.raises(exceptions.SimCardsNotFound) as e:
            self.simcard.get_sim_card(imsi)
        assert str(e.value) == expected_message

    def test_get_sim_card(self, make_sim_card_models):
        imsi = "758496758355001"
        sim_models = make_sim_card_models(start_with=0, end_with=2)

        self.simcard.sim_repository.get_sim_cards = MagicMock(
            return_value=iter(sim_models)
        )
        result = self.simcard.get_sim_card(imsi)
        self.simcard.sim_repository.get_sim_cards.assert_called_once()
        assert result == sim_models[0]

    def test_activate_sim_using_provisioning(
        self, make_sim_provider_log, make_sim_activity_log
    ):
        work_id = "c19d2f7d-d686-472a-ac00-fdb858546b82"
        status = "Active"
        audit_date = datetime.today()
        sim_activity = make_sim_activity_log()
        sim_provider_log = make_sim_provider_log(work_id, status, audit_date)
        self.simcard.provisioning.activate_sim = MagicMock(
            return_value=sim_provider_log
        )
        self.simcard.sim_repository.update_sim_card = MagicMock(return_value=None)
        result = self.simcard.activate_sim_using_provisioning(
            sim_activity.imsi,
            sim_activity.msisdn,
            sim_activity.prior_value,
            sim_activity.client_ip,
        )
        self.simcard.sim_repository.update_sim_card.assert_called_once()
        assert result == sim_provider_log

    def test_activate_sim_using_provisioning_when_sim_provider_log_status_invalid(
        self, make_sim_provider_log, make_sim_activity_log
    ):
        work_id = "c19d2f7d-d686-472a-ac00-fdb858546b82"
        status = "Invalid"
        audit_date = datetime.today()
        expected_message = "SIM already activated."
        sim_activity = make_sim_activity_log()
        sim_provider_log = make_sim_provider_log(work_id, status, audit_date)

        self.simcard.provisioning.activate_sim = MagicMock(
            return_value=sim_provider_log
        )
        with pytest.raises(exceptions.SimActivationError) as e:
            self.simcard.activate_sim_using_provisioning(
                sim_activity.imsi,
                sim_activity.msisdn,
                sim_activity.prior_value,
                sim_activity.client_ip,
            )
        assert str(e.value) == expected_message

    def test_create_sim_response(self, make_sim_activate_response):
        sim_response = make_sim_activate_response(
            sim_status=SimStatus.ACTIVE, message="Queued"
        )
        audit_uuid = "b93a6553-ddf2-4ce4-9a62-e9c588cf4524"
        result = self.simcard.create_sim_response(audit_uuid, sim_response)
        assert result == sim_response

    def test_activate_sim(
        self, make_sim_card_model, make_sim_provider_log, make_sim_activate_response
    ):
        sim_model = make_sim_card_model(sim_status=sim_status.ACTIVE)
        created_by = "user"
        work_id = "c19d2f7d-d686-472a-ac00-fdb858546b82"
        status = "Invalid"
        audit_date = datetime.today()
        sim_activate_response = make_sim_activate_response(
            sim_status=SimStatus.ACTIVE, message="Queued"
        )
        self.simcard.get_sim_card = MagicMock(return_value=sim_model)
        self.simcard.create_sim_activity_log = MagicMock(
            return_value=uuid.UUID("780ba803-5117-45ce-8272-784f18e894f4")
        )
        self.simcard.activate_sim_using_provisioning = MagicMock(
            return_value=make_sim_provider_log(work_id, status, audit_date)
        )
        self.simcard.audit_service.add_sim_audit_api = MagicMock(
            return_value={"id": "89496851651653"}
        )
        self.simcard.audit_service.add_sim_provider_audit_api = MagicMock(
            return_value=None
        )
        self.simcard.create_sim_response = MagicMock(return_value=sim_activate_response)
        result = self.simcard.activate_sim(sim_model.imsi, created_by)
        assert result == sim_activate_response
        self.simcard.get_sim_card.assert_called_once()

        self.simcard.audit_service.add_sim_audit_api.assert_called_once()
        self.simcard.audit_service.add_sim_provider_audit_api.assert_called_once()
        self.simcard.create_sim_response.assert_called_once()

    def test_deactivate_sim_using_suspend_when_ppl_status_is_Error(
        self, make_sim_provider_log
    ):
        imsi = "234588570011234"
        msisdn = "883200000110321"
        client_ip = "127.0.0.1"
        work_id = "c19d2f7d-d686-472a-ac00-fdb858546b82"
        status = "Error"
        expected_message = "SIM already deactivated."
        audit_date = datetime.today()
        sim_provider_log = make_sim_provider_log(
            work_id, status, audit_date, prior_status=sim_status.ACTIVE
        )
        self.simcard.provisioning.suspend_sim = MagicMock(return_value=sim_provider_log)
        with pytest.raises(exceptions.SimDeActivationError) as e:
            self.simcard.deactivate_sim_using_suspend(
                imsi, msisdn, sim_status.ACTIVE, client_ip
            )
        assert str(e.value) == expected_message
        self.simcard.provisioning.suspend_sim.assert_called_once()

    def test_deactivate_sim_using_suspend(self, make_sim_provider_log):
        imsi = ("234588570011234",)
        msisdn = ("883200000110321",)
        client_ip = ("127.0.0.1",)
        work_id = "c19d2f7d-d686-472a-ac00-fdb858546b82"
        status = "Success"
        audit_date = datetime.today()
        sim_provider_log = make_sim_provider_log(
            work_id, status, audit_date, prior_status=sim_status.ACTIVE
        )
        self.simcard.provisioning.suspend_sim = MagicMock(return_value=sim_provider_log)
        self.simcard.sim_repository.update_sim_card = MagicMock(return_value=None)
        result = self.simcard.deactivate_sim_using_suspend(
            imsi, msisdn, sim_status.ACTIVE, client_ip
        )
        assert result == sim_provider_log
        self.simcard.provisioning.suspend_sim.assert_called_once()
        self.simcard.sim_repository.update_sim_card.assert_called_once()

    def test_create_sim_response_suspend(self, make_sim_deactivate_response):
        sim_deactivate_response = make_sim_deactivate_response(
            sim_status=SimStatus.DEACTIVATED, message="Queued"
        )
        audit_uuid = "792dad14-7c0a-4639-81fc-6757de3d33a2"
        result = self.simcard.create_sim_response_suspend(
            audit_uuid, sim_deactivate_response
        )
        assert result == sim_deactivate_response

    def test_suspend_sim(
        self, make_sim_card_model, make_sim_deactivate_response, make_sim_provider_log
    ):
        sim_model = make_sim_card_model(sim_status=sim_status.DEACTIVATED)
        sim_deactivate_response = make_sim_deactivate_response(
            sim_status=SimStatus.DEACTIVATED, message="Queued"
        )
        created_by = "user"
        work_id = "c19d2f7d-d686-472a-ac00-fdb858546b82"
        status = "Invalid"
        audit_date = datetime.today()
        client_ip = "127.0.0.1"
        sim_provider_log = make_sim_provider_log(
            work_id, status, audit_date, prior_status=sim_status.DEACTIVATED
        )
        self.simcard.get_sim_card = MagicMock(return_value=sim_model)
        self.simcard.create_sim_activity_log = MagicMock(
            return_value=uuid.UUID("a26082eb-36c5-47fe-9503-4801c09baab3")
        )
        self.simcard.deactivate_sim_using_suspend = MagicMock(
            return_value=sim_provider_log
        )

        self.simcard.audit_service.add_sim_audit_api = MagicMock(
            return_value={"id": "89496851651653"}
        )
        self.simcard.audit_service.add_sim_provider_audit_api = MagicMock(
            return_value=None
        )
        self.simcard.create_sim_response_suspend = MagicMock(
            return_value=sim_deactivate_response
        )
        result = self.simcard.suspend_sim(sim_model.imsi, created_by, client_ip)
        assert result == sim_deactivate_response

    def test_audit_logs_when_no_auditlog_exist(self, make_auditlog):
        imsi = "234588570011234"
        month = Month(year=2023, month=7, day=1)
        self.simcard.audit_service.get_sim_audit_api = MagicMock(
            return_value=AuditResult(results=[make_auditlog()], totalCount=1)
        )
        self.simcard.audit_service.get_sim_audit_api = MagicMock(
            side_effect=exceptions.NoAuditLogs(imsi, month)
        )
        expected_message = (
            f"No audit logs found for IMSI {imsi} on the specified date {month}."
        )
        with pytest.raises(exceptions.NoAuditLogs) as e:
            self.simcard.audit_logs(imsi, month)
        assert str(e.value) == expected_message
        self.simcard.audit_service.get_sim_audit_api.assert_called_once()

    def test_audit_logs(self, make_auditlog):
        imsi = "234588570011234"
        month = Month(year=2023, month=7, day=1)
        auditlog = make_auditlog()

        expected_response = AuditResult(results=[auditlog], totalCount=1)
        self.simcard.audit_service.get_sim_audit_api = MagicMock(
            return_value=expected_response
        )
        result = self.simcard.audit_logs(imsi, month)
        assert result == ([auditlog], 1)
        assert result[1] == 1

    @pytest.mark.skip(
        reason="I need check real function working rate_plan_id not accept None value"
    )
    def test_get_connection_summary(self, make_ConnectionSummary):
        imsi = "234588570011234"
        ConnectionSummary = make_ConnectionSummary()
        self.simcard.cdr_repository.get_cdr_last_session = MagicMock(
            return_value=datetime.now()
        )
        self.simcard.sim_repository.get_connection_summary = MagicMock(
            return_value=ConnectionSummary
        )
        result = self.simcard.get_connection_summary(imsi)
        assert result == ConnectionSummary
        self.simcard.cdr_repository.get_cdr_last_session.assert_called_once()
        self.simcard.sim_repository.get_connection_summary.assert_called_once()

    def test_get_connection_summary_when_rateplan_exist(
        self, make_ConnectionSummary, make_rate_plan
    ):
        imsi = "234588570011234"
        ConnectionSummary = make_ConnectionSummary()
        rate_plan = make_rate_plan()
        self.simcard.cdr_repository.get_cdr_last_session = MagicMock(
            return_value=datetime.now()
        )
        self.simcard.sim_repository.get_connection_summary = MagicMock(
            return_value=ConnectionSummary
        )
        self.simcard.rate_plan_repository.get = MagicMock(return_value=rate_plan)
        result = self.simcard.get_connection_summary(imsi)
        assert result.rate_plan == ConnectionSummary.rate_plan
        self.simcard.cdr_repository.get_cdr_last_session.assert_called_once()
        self.simcard.sim_repository.get_connection_summary.assert_called_once()

    def test_get_connection_summary_when_rateplan_is_null(
        self,
        make_ConnectionSummary,
    ):
        imsi = "234588570011234"
        ConnectionSummary = make_ConnectionSummary()
        self.simcard.cdr_repository.get_cdr_last_session = MagicMock(
            return_value=datetime.now()
        )
        self.simcard.sim_repository.get_connection_summary = MagicMock(
            return_value=ConnectionSummary
        )
        self.simcard.rate_plan_repository.get = MagicMock(return_value=None)
        result = self.simcard.get_connection_summary(imsi)
        assert not result.rate_plan
        self.simcard.cdr_repository.get_cdr_last_session.assert_called_once()
        self.simcard.sim_repository.get_connection_summary.assert_called_once()

    def test_get_sim_usage_when_usage_count_is_null(self):
        self.simcard.sim_repository.get_carrier_name = MagicMock()
        self.simcard.sim_repository.get_sim_usage_count = MagicMock(
            return_value=(0, 0, 0, 0, 0)
        )
        expected_message = "No IMSI found."
        with pytest.raises(exceptions.NoSimFound) as e:
            self.simcard.get_sim_usage(1)
        assert str(e.value) == expected_message
        self.simcard.sim_repository.get_sim_usage_count.assert_called_once()

    def test_get_sim_usage_when_rate_plan_not_exist(self, make_sim_card_models):
        make_sim_card_models = make_sim_card_models()
        self.simcard.sim_repository.get_sim_usage_count = MagicMock(
            return_value=(10, 2, 4, 2, 2)
        )
        self.simcard.get_sim_cards = MagicMock(return_value=make_sim_card_models)
        expected_message = "Rate Plan not found."
        with pytest.raises(exceptions.RatePlanNotFound) as e:
            self.simcard.get_sim_usage(1)
        assert str(e.value) == expected_message
        self.simcard.sim_repository.get_sim_usage_count.assert_called_once()

    def test_get_sim_usage(
        self,
        make_sim_card_models,
        make_usage_model,
    ):
        make_sim_card_models = make_sim_card_models(rate_plan_id=1)
        usage = make_usage_model(start_with=0, end_with=1)
        self.simcard.sim_repository.get_carrier_name = MagicMock()
        self.simcard.sim_repository.get_sim_usage_count = MagicMock(
            return_value=(10, 2, 4, 2, 2)
        )
        self.simcard.sim_repository.get_sim_imsi_by_account_id = MagicMock(
            return_value=(
                [
                    "***************",
                    "***************",
                    "***************",
                    "***************",
                    "***************",
                ]
            )
        )
        self.simcard.cdr_repository.get_sim_usage_total_by_imsi = MagicMock(
            return_value=5001
        )
        self.simcard.get_sim_cards = MagicMock(return_value=make_sim_card_models)
        self.simcard.sim_repository.get_sim_usage = MagicMock(side_effect=usage)
        self.simcard.cdr_repository.get_sim_usage_by_imsi = MagicMock(
            return_value=list()
        )
        self.simcard.market_share_api.get_imsis_usage = MagicMock()
        self.simcard._merge_sim_managment_with_usage = MagicMock(side_effect=usage)
        result = self.simcard.get_sim_usage(account_id=1)

        statistics_dict = {
            "totalUsage": 5001,
            "totalEEUsageData": 0,
            "totalActiveSims": 2,
            "totalReadyActivationSims": 2,
            "totalDeactivatedSims": 4,
            "totalPendingSims": 2,
            "totalSims": 10,
        }

        self.simcard.sim_repository.get_carrier_name.assert_called_once()
        self.simcard.sim_repository.get_sim_usage_count.assert_called_once()
        self.simcard.get_sim_cards.assert_called_once()
        self.simcard.sim_repository.get_sim_usage.assert_called_once()
        self.simcard.cdr_repository.get_sim_usage_by_imsi.assert_called_once()
        self.simcard._merge_sim_managment_with_usage.assert_called_once()

        result_usage, total, result_summary = result

        assert result_usage == usage[0]
        assert total == 10
        assert result_summary == statistics_dict

    def test_merge_sim_managment_with_usage(self):
        pass

    def test_merge_sim_with_usage(self):
        pass

    def test_cards_active_statistic(self):
        pass

    def test_connection_history(self):
        pass

    def test_get_month_usage(self):
        pass

    def test_sim_status_details(self):
        pass

    def test__get_sim_card(self):
        pass

    def test__add_sim_monthly_status(self):
        pass

    def test_update_sim_card_based_on_response(self):
        pass

    def test_update_sim_card_by_imsi(self):
        pass

    def test_bulk_background_process(self):
        pass

    def test_get_imsis(self):
        pass

    def test_copy_monthly_statistics(self, make_monthly_sim_status):
        monthly_sim_status = make_monthly_sim_status()
        self.simcard.sim_repository.copy_monthly_statistics = MagicMock(
            return_value=iter([monthly_sim_status])
        )
        expected_value = 1
        self.simcard.sim_repository.add_bulk_sim_monthly_statistics = MagicMock(
            return_value=1
        )
        result = self.simcard.copy_monthly_statistics()
        assert result == expected_value
        self.simcard.sim_repository.add_bulk_sim_monthly_statistics.assert_called_once()
        self.simcard.sim_repository.add_bulk_sim_monthly_statistics.assert_called_once()

    # Marketshare Service Test cases

    def test_get_market_share(
        self, mock_period, mock_market_share_usage, mock_sim_card_imsi
    ):
        mock_period = mock_period()
        mock_market_share_usage = mock_market_share_usage()
        mock_sim_card_imsi = mock_sim_card_imsi()
        self.simcard.sim_repository.get_market_share = MagicMock(
            return_value=iter([mock_sim_card_imsi])
        )
        self.simcard.market_share_api.market_share_usage = MagicMock(
            return_value=mock_market_share_usage
        )
        result = self.simcard.get_market_share(mock_period)
        assert result == mock_market_share_usage
        self.simcard.sim_repository.get_market_share.assert_called_once()
        self.simcard.market_share_api.market_share_usage.assert_called_once()

    def test_get_market_share_by_account(
        self, mock_market_share_usage, mock_sim_card_imsi, mockMarketShareData
    ):
        mock_market_share_usage = mock_market_share_usage()
        sim_card_imsi = mock_sim_card_imsi()
        mockMarketShareData = mockMarketShareData()
        self.simcard.sim_repository.get_market_share_by_account = MagicMock(
            return_value=iter([sim_card_imsi])
        )
        self.simcard.market_share_api.market_share_usage = MagicMock(
            return_value=mock_market_share_usage
        )
        result = self.simcard.get_market_share_by_account(mockMarketShareData)
        assert result == mock_market_share_usage
        self.simcard.sim_repository.get_market_share_by_account.assert_called_once()
        self.simcard.market_share_api.market_share_usage.assert_called_once()

    def test_voice_connection_history(self, make_voice_connection_models):
        voice_records = make_voice_connection_models(start_with=0, end_with=1)
        self.simcard.sim_repository.check_imsi_account = MagicMock(return_value=1)
        self.simcard.cdr_repository.voice_connection_history = MagicMock(
            return_value=voice_records
        )
        self.simcard.cdr_repository.voice_connection_history_count = MagicMock(
            return_value=10
        )
        voice_history, voice_count = self.simcard.voice_connection_history(
            imsi="************001", month=datetime.now()
        )
        for records in voice_history:
            assert records.call_minutes == 15
            assert records.call_number == "********"
        assert voice_count == 10

    def test_get_market_share_with_imsi_not_found(
        self, mock_period, mock_market_share_usage, mock_CarrierName
    ):
        mock_period = mock_period()
        mock_market_share_usage = mock_market_share_usage()
        mock_CarrierName = mock_CarrierName()
        self.simcard.sim_repository.get_carrier_name = MagicMock(
            return_value=iter([mock_CarrierName])
        )
        self.simcard.sim_repository.get_market_share = MagicMock(return_value=[])
        self.simcard.market_share_api.market_share_usage = MagicMock(
            return_value=mock_market_share_usage
        )
        with pytest.raises(NotFound) as exc_info:
            self.simcard.get_market_share(mock_period)
        msg = "IMSI Not found with any account id"
        assert str(exc_info.value) == msg
        self.simcard.sim_repository.get_carrier_name.assert_called_once()

    def test_get_market_share_by_account_with_summary_null(
        self,
        mockMarketShareData,
        mock_CarrierName,
        mock_sim_card_imsi,
        mock_market_share_usage_with_null_summary,
    ):
        mockMarketShareData = mockMarketShareData()
        mock_CarrierName = mock_CarrierName()
        sim_card_imsi = mock_sim_card_imsi()
        null_summary = mock_market_share_usage_with_null_summary()
        self.simcard.sim_repository.get_carrier_name = MagicMock(
            return_value=iter([mock_CarrierName])
        )
        self.simcard.sim_repository.get_market_share_by_account = MagicMock(
            return_value=iter([sim_card_imsi])
        )
        self.simcard.market_share_api.market_share_usage = MagicMock(
            return_value=null_summary
        )
        with pytest.raises(NotFound) as exc_info:
            self.simcard.get_market_share_by_account(mockMarketShareData)
        msg = "Response is received but no summary data found"
        assert str(exc_info.value) == msg
        self.simcard.sim_repository.get_carrier_name.assert_called_once()
        self.simcard.sim_repository.get_market_share_by_account.assert_called_once()
        self.simcard.market_share_api.market_share_usage.assert_called_once()

    def test_sim_type_not_available(self):
        remains = [
            (1, FormFactor.STANDARD.name, 1),
            (2, FormFactor.MICRO.name, 15),
        ]
        self.simcard.get_sim_remains = MagicMock(return_value=remains)
        form_factor = FormFactor.NANO.name
        with pytest.raises(exceptions.SimTypeNotAvailable) as exc_info:
            self.simcard.check_sim_type_available(form_factor)
        msg = f"Sim type {form_factor} not available for allocation"
        assert str(exc_info.value) == msg

    def test_get_market_share_imsi(self, mock_market_share_usage, mock_period):
        mock_market_share_usage = mock_market_share_usage()
        self.simcard.market_share_api.market_share_usage = MagicMock(
            return_value=mock_market_share_usage
        )

        mock_period = mock_period()
        result = self.simcard.get_market_share_imsi(
            period=mock_period, imsi=IMSI("************001")
        )

        assert result == mock_market_share_usage
        self.simcard.market_share_api.market_share_usage.assert_called_once()

    def test_imsi_wise_error_already_allocated(self, make_custom_models):
        imsi_list = ["************000", "************001"]
        form_code = "STANDARD"
        custom_models = make_custom_models(start_with=0, end_with=2)
        self.simcard.sim_repository.get_all_imsi = MagicMock(
            return_value=iter(custom_models)
        )
        json_error, valid_imsi, total_sim, error_sim = self.simcard.imsi_wise_error(
            imsi_list=imsi_list, form_code=form_code
        )

        assert valid_imsi == 2
        assert total_sim == [
            {"sim": "************000", "issue": "Already allocated to None Account"},
            {"sim": "************001", "issue": "Already allocated to None Account"},
        ]
        assert error_sim == []

    def test_imsi_wise_error_not_available(self, make_custom_models):
        imsi_list = ["************000", "************001"]
        form_code = "NANO"
        custom_models = make_custom_models(start_with=0, end_with=2)
        self.simcard.sim_repository.get_all_imsi = MagicMock(
            return_value=iter(custom_models)
        )
        self.simcard.sim_repository.get_all_imsi = MagicMock(return_value=custom_models)
        json_error, valid_imsi, total_sim, error_sim = self.simcard.imsi_wise_error(
            imsi_list=imsi_list, form_code=form_code
        )
        assert valid_imsi == 2
        assert total_sim == [
            {"sim": "************000", "issue": "IMSI does not belong NANO sim type"},
            {"sim": "************001", "issue": "IMSI does not belong NANO sim type"},
        ]
        assert error_sim == []

    def test_check_imsi_account_true(self):
        account_id = 1
        imsi = "************000"
        self.simcard.sim_repository.check_imsi_account = MagicMock(return_value=1)
        response = self.simcard.check_imsi_account(account_id, imsi)
        expected_response = True
        assert response == expected_response

    def test_check_imsi_account_false(self):
        account_id = 1
        imsi = "************000"
        self.simcard.sim_repository.check_imsi_account = MagicMock(return_value=None)
        response = self.simcard.check_imsi_account(account_id, imsi)
        expected_response = False
        assert response == expected_response

    def test_re_allocation_validation_success(self, create_rate_plan):
        self.simcard.rate_plan_repository.get(return_value=create_rate_plan)
        self.simcard.sim_repository.get_allocation_count_by_rate_plan = MagicMock(
            return_value=5
        )
        with pytest.raises(exceptions.RatePlanNotFound) as exc_info:
            self.simcard.re_allocation_validation(
                account_id=1,
                rate_plan_id=1,
                imsi_list=["************000"],
                # re_allocated_by="test",
            )
        msg = f"Rate Plan with id:{1} does not exist."
        assert str(exc_info.value) == msg

    def test_get_msisdn_export_empty(self):

        self.simcard.sim_repository.get_msisdn_export = MagicMock(return_value=[])

        response = self.simcard.sim_repository.get_msisdn_export()

        assert isinstance(response, list)
        assert len(response) == 0

    def test_create_msisdn_response_success(self):
        invalid_format = ["123", "456"]
        pool_msisdn = ["**********"]
        sim_card_msisdn = ["**********"]
        duplicate_msisdn = ["**********"]

        response = self.simcard.creat_msisdn_response(
            invalid_format=invalid_format,
            pool_msisdn=pool_msisdn,
            sim_card_msisdn=sim_card_msisdn,
            duplicate_msisdn=duplicate_msisdn,
        )

        assert isinstance(response, model.MsisdnResult)
        assert response.error_msisdn == 5
        assert len(response.error_result) == 5
        assert response.error_result[0]["msisdn"] == "123"
        assert response.error_result[0]["issue"] == "Invalid MSISDN format"
        assert response.error_result[2]["issue"] == "Duplicate MSISDN"
        assert response.error_result[3]["issue"] == "Already uploaded"

    def test_create_msisdn_response_no_errors(self):
        invalid_format = []
        pool_msisdn = []
        sim_card_msisdn = []
        duplicate_msisdn = []

        response = self.simcard.creat_msisdn_response(
            invalid_format=invalid_format,
            pool_msisdn=pool_msisdn,
            sim_card_msisdn=sim_card_msisdn,
            duplicate_msisdn=duplicate_msisdn,
        )

        assert isinstance(response, model.MsisdnResult)
        assert response.error_msisdn == 0
        assert response.error_result == []

    def test_get_msisdn_export_success(self):
        mock_msisdn_data = [
            model.MsisdnDetails(
                id=1,
                msisdn="**********",
                created_at=datetime(2023, 3, 11, 21, 13, 44),
                sim_profile=model.SimProfile.DATA_ONLY,
                msisdn_factor=model.MSISDNFactor.INTERNATIONAL,
                uploaded_by="User1",
                country="United Kingdom",
                sim_provider="NR",
                account_name="Account1",
                logo_key=None,
                logo_url=None,
            )
        ]

        self.simcard.get_msisdn_export = MagicMock(return_value=iter(mock_msisdn_data))

        response = list(self.simcard.get_msisdn_export())

        assert len(response) == 1
        assert isinstance(response, list)
        assert isinstance(response[0], model.MsisdnDetails)
        assert response[0].msisdn == "**********"

    def test_get_msisdn_export_not_found(self):
        self.simcard.get_msisdn_export = MagicMock(
            side_effect=exceptions.NotFound("No msisdn details found.")
        )

        with pytest.raises(exceptions.NotFound, match="No msisdn details found."):
            list(self.simcard.get_msisdn_export())

    def test_get_msisdn_factor_success(self):

        mock_msisdn = ["**********"]
        msisdn_factor = model.MSISDNFactor.INTERNATIONAL

        self.simcard.sim_repository.get_msisdn_factor = MagicMock(
            return_value=mock_msisdn
        )

        response = self.simcard.get_msisdn_factor(msisdn_factor=msisdn_factor)

        assert response == mock_msisdn[0]

    def test_get_msisdn_factor_not_found(self):

        msisdn_factor = model.MSISDNFactor.INTERNATIONAL

        self.simcard.sim_repository.get_msisdn_factor = MagicMock(return_value=None)

        with pytest.raises(
            exceptions.NotFound,
            match=f"No free MSISDN found with factor {msisdn_factor}",
        ):
            self.simcard.get_msisdn_factor(msisdn_factor=msisdn_factor)

    def test_update_sim_card_success(self):
        trace_id_var.set(uuid4())
        imsi = "**********12345"
        msisdn = "**********"
        sim_profile = model.SimProfile.DATA_ONLY
        client_ip = "***********"
        created_by = "admin"

        mock_sim_card = MagicMock()
        mock_sim_card.sim_status = SimStatus.READY_FOR_ACTIVATION
        mock_sim_card.imsi = imsi
        mock_sim_card.msisdn = msisdn
        mock_sim_card.iccid = "********************"
        mock_sim_card.sim_profile = sim_profile

        self.simcard.get_sim_card = MagicMock(return_value=mock_sim_card)
        self.simcard.sim_repository.update_sim_card_details_by_imsi = MagicMock(
            return_value=True
        )
        msisdn_value = msisdn
        msisdn_sim_profile = sim_profile
        existing_imsi = None
        allocation_id = 167
        existing_msisdn = "**********1"
        self.simcard.sim_repository.validate_sim_card_details_by_imsi = MagicMock(
            return_value=(
                msisdn_value,
                msisdn_sim_profile,
                existing_imsi,
                allocation_id,
                existing_msisdn,
            )
        )
        self.simcard.audit_service.add_upload_msisdn_audit_api = MagicMock()
        self.simcard.suspend_sim = MagicMock()

        response = self.simcard.update_sim_card_details_by_imsi(
            imsi, msisdn, sim_profile, client_ip, created_by
        )

        assert response.imsi == imsi
        assert response.msisdn == msisdn
        assert response.sim_profile == sim_profile

    def test_update_sim_card_pending_status(self):
        imsi = "**********12345"
        msisdn = "**********"
        sim_profile = model.SimProfile.DATA_ONLY
        client_ip = "***********"

        mock_sim_card = MagicMock()
        mock_sim_card.sim_status = SimStatus.PENDING

        self.simcard.get_sim_card = MagicMock(return_value=mock_sim_card)

        with pytest.raises(
            exceptions.IMSIError, match="Cannot update Pending or Active Sim."
        ):
            self.simcard.update_sim_card_details_by_imsi(
                imsi, msisdn, sim_profile, client_ip
            )

    def test_update_sim_card_unexpected_error(self):
        imsi = "**********12345"
        msisdn = "**********"
        sim_profile = model.SimProfile.DATA_ONLY
        client_ip = "***********"

        mock_sim_card = MagicMock()
        mock_sim_card.sim_status = SimStatus.ACTIVE

        self.simcard.get_sim_card = MagicMock(return_value=mock_sim_card)
        self.simcard.sim_repository.update_sim_card_details_by_imsi = MagicMock(
            return_value=False
        )

        msisdn_value = msisdn
        msisdn_sim_profile = sim_profile
        existing_imsi = None
        allocation_id = 167
        existing_msisdn = "**********1"

        self.simcard.sim_repository.validate_sim_card_details_by_imsi = MagicMock(
            return_value=(
                msisdn_value,
                msisdn_sim_profile,
                existing_imsi,
                allocation_id,
                existing_msisdn,
            )
        )

        with pytest.raises(
            exceptions.IMSIError, match="Cannot update Pending or Active Sim."
        ):
            self.simcard.update_sim_card_details_by_imsi(
                imsi, msisdn, sim_profile, client_ip
            )

    def test_update_single_sim_card_success(self):
        imsi = "**********12345"
        msisdn = "**********"
        sim_profile = model.SimProfile.DATA_ONLY
        client_ip = "***********"
        uploaded_by = "admin"
        valid_data = [
            {
                "IMSI": imsi,
                "MSISDN": msisdn,
            }
        ]

        mock_sim_card = MagicMock()
        mock_sim_card.imsi = imsi
        mock_sim_card.msisdn = msisdn
        mock_sim_card.sim_profile = sim_profile
        mock_sim_card.allocation_id = None

        self.simcard.validate_common_request = MagicMock(return_value=True)
        self.simcard.sim_repository.get_sim_cards.return_value = [mock_sim_card]
        self.simcard.sim_repository.validate_bulk_sim_card_details = MagicMock(
            return_value=(
                [
                    model.SimCardData(
                        requested_imsi=imsi,
                        requested_msisdn=msisdn,
                        allocation_id=1,
                        existing_msisdn="**********",
                        msisdn_value=msisdn,
                        msisdn_sim_profile=sim_profile,
                        msisdn_factor="INTERNATIONAL",
                        existing_imsi="**********12345",
                    )
                ]
            )
        )
        self.simcard.sim_repository.bulk_update_sim_card_details = MagicMock(
            return_value=True
        )

        response = self.simcard.update_sim_card_details(
            imsi_list=[imsi],
            sim_profile=sim_profile,
            total_records=1,
            msisdn_factor=model.MSISDNFactor.INTERNATIONAL,
            client_ip=client_ip,
            uploaded_by=uploaded_by,
            msisdn_list=[msisdn],
            valid_data=valid_data,
        )

        assert response.total_details == 1

    def test_update_multiple_sim_cards_success(self):
        imsi_list = ["**********12345", "**********12346"]
        msisdn_list = ["**********", "9876543211"]
        sim_profile = model.SimProfile.DATA_ONLY
        client_ip = "***********"
        uploaded_by = "admin"
        valid_data = [
            {
                "IMSI": "**********12345",
                "MSISDN": "**********",
            },
            {
                "IMSI": "**********12346",
                "MSISDN": "9876543211",
            },
        ]

        mock_sim_cards = [
            MagicMock(
                imsi=imsi, msisdn=msisdn, sim_profile=sim_profile, allocation_id=None
            )
            for imsi, msisdn in zip(imsi_list, msisdn_list)
        ]

        self.simcard.validate_common_request = MagicMock(return_value=True)
        self.simcard.sim_repository.get_sim_cards.return_value = mock_sim_cards
        self.simcard.sim_repository.get_msisdn_factor = MagicMock(
            return_value=["**********", "**********1"]
        )
        self.simcard.sim_repository.validate_bulk_sim_card_details = MagicMock(
            return_value=[
                model.SimCardData(
                    requested_imsi=imsi,
                    requested_msisdn=msisdn,
                    allocation_id=1,
                    existing_msisdn="**********",
                    msisdn_value=msisdn,
                    msisdn_sim_profile=sim_profile,
                    msisdn_factor="INTERNATIONAL",
                    existing_imsi=imsi,
                )
                for imsi, msisdn in zip(imsi_list, msisdn_list)
            ]
        )
        self.simcard.sim_repository.bulk_update_sim_card_details = MagicMock(
            return_value=True
        )

        response = self.simcard.update_sim_card_details(
            imsi_list=imsi_list,
            sim_profile=sim_profile,
            total_records=2,
            msisdn_factor=model.MSISDNFactor.INTERNATIONAL,
            client_ip=client_ip,
            uploaded_by=uploaded_by,
            msisdn_list=msisdn_list,
            valid_data=valid_data,
        )

        assert response.total_details == 2

    def test_update_sim_card_no_free_msisdn(self):
        sim_profile = model.SimProfile.DATA_ONLY
        client_ip = "***********"
        uploaded_by = "admin"
        valid_data = [
            {
                "IMSI": "**********12345",
                "MSISDN": "**********",
            }
        ]

        self.simcard.validate_common_request = MagicMock(return_value=True)
        self.simcard.sim_repository.get_msisdn_factor = MagicMock(return_value=[])

        with pytest.raises(exceptions.MSISDNNotFound, match="No free MSISDNs found."):
            self.simcard.update_sim_card_details(
                imsi_list=[],
                sim_profile=sim_profile,
                total_records=1,
                msisdn_factor=model.MSISDNFactor.INTERNATIONAL,
                client_ip=client_ip,
                uploaded_by=uploaded_by,
                msisdn_list=["**********"],
                valid_data=valid_data,
            )

    def test_update_sim_card_bulk_update_failure(self):
        imsi_list = ["**********12345"]
        msisdn_list = ["**********"]
        sim_profile = model.SimProfile.DATA_ONLY
        client_ip = "***********"
        uploaded_by = "admin"
        valid_data = [
            {
                "IMSI": "**********12345",
                "MSISDN": "**********",
            }
        ]

        mock_sim_card = MagicMock()
        mock_sim_card.imsi = imsi_list[0]
        mock_sim_card.msisdn = msisdn_list[0]
        mock_sim_card.sim_profile = sim_profile
        mock_sim_card.allocation_id = None

        self.simcard.validate_common_request = MagicMock(return_value=True)
        self.simcard.sim_repository.get_sim_cards.return_value = [mock_sim_card]
        self.simcard.sim_repository.validate_bulk_sim_card_details = MagicMock(
            return_value=[
                model.SimCardData(
                    requested_imsi=imsi_list[0],
                    requested_msisdn=msisdn_list[0],
                    allocation_id=1,
                    existing_msisdn="**********",
                    msisdn_value=msisdn_list[0],
                    msisdn_sim_profile=sim_profile,
                    msisdn_factor="INTERNATIONAL",
                    existing_imsi=imsi_list[0],
                )
            ]
        )
        self.simcard.sim_repository.bulk_update_sim_card_details = MagicMock(
            return_value=False
        )

        with pytest.raises(exceptions.IMSIError, match="Unexpected error occoured."):
            self.simcard.update_sim_card_details(
                imsi_list=imsi_list,
                sim_profile=sim_profile,
                total_records=1,
                msisdn_factor=model.MSISDNFactor.INTERNATIONAL,
                client_ip=client_ip,
                uploaded_by=uploaded_by,
                msisdn_list=msisdn_list,
                valid_data=valid_data,
            )

    def test_update_sim_card_bulk_update_mix_profile(self):
        imsi_list = ["**********12345"]
        msisdn_list = ["**********"]
        sim_profile = model.SimProfile.DATA_ONLY
        client_ip = "***********"
        uploaded_by = "admin"
        valid_data = [
            {
                "IMSI": "**********12345",
                "MSISDN": "**********",
            }
        ]

        mock_sim_card = MagicMock()
        mock_sim_card.imsi = imsi_list[0]
        mock_sim_card.msisdn = msisdn_list[0]
        mock_sim_card.sim_profile = sim_profile
        mock_sim_card.allocation_id = None

        self.simcard.sim_repository.validate_msisdn_update_request = MagicMock(
            return_value=False
        )
        self.simcard.sim_repository.get_sim_cards.return_value = [mock_sim_card]
        self.simcard.sim_repository.validate_bulk_sim_card_details = MagicMock(
            return_value=[
                model.SimCardData(
                    requested_imsi=imsi_list[0],
                    requested_msisdn=msisdn_list[0],
                    allocation_id=1,
                    existing_msisdn="**********",
                    msisdn_value=msisdn_list[0],
                    msisdn_sim_profile=sim_profile,
                    msisdn_factor="INTERNATIONAL",
                    existing_imsi=imsi_list[0],
                )
            ]
        )
        self.simcard.sim_repository.bulk_update_sim_card_details = MagicMock(
            return_value=False
        )

        with pytest.raises(
            exceptions.IMSIError,
            match="The requested MSISDNs have different MSISDN "
            "Type or belong to different SIM Profile.",
        ):
            self.simcard.update_sim_card_details(
                imsi_list=imsi_list,
                sim_profile=sim_profile,
                total_records=1,
                msisdn_factor=model.MSISDNFactor.INTERNATIONAL,
                client_ip=client_ip,
                uploaded_by=uploaded_by,
                msisdn_list=msisdn_list,
                valid_data=valid_data,
            )

    def test_update_sim_card_bulk_update_mix_msisdn_type(self):
        imsi_list = ["**********12345"]
        msisdn_list = ["**********"]
        sim_profile = model.SimProfile.DATA_ONLY
        client_ip = "***********"
        uploaded_by = "admin"
        valid_data = [
            {
                "IMSI": "**********12345",
                "MSISDN": "**********",
            }
        ]

        mock_sim_card = MagicMock()
        mock_sim_card.imsi = imsi_list[0]
        mock_sim_card.msisdn = msisdn_list[0]
        mock_sim_card.sim_profile = sim_profile
        mock_sim_card.allocation_id = None

        self.simcard.sim_repository.validate_msisdn_update_request = MagicMock(
            return_value=False
        )
        self.simcard.sim_repository.get_sim_cards.return_value = [mock_sim_card]
        self.simcard.sim_repository.validate_bulk_sim_card_details = MagicMock(
            return_value=[
                model.SimCardData(
                    requested_imsi=imsi_list[0],
                    requested_msisdn=msisdn_list[0],
                    allocation_id=1,
                    existing_msisdn="**********",
                    msisdn_value=msisdn_list[0],
                    msisdn_sim_profile=sim_profile,
                    msisdn_factor="INTERNATIONAL",
                    existing_imsi=imsi_list[0],
                )
            ]
        )
        self.simcard.sim_repository.bulk_update_sim_card_details = MagicMock(
            return_value=False
        )

        with pytest.raises(
            exceptions.IMSIError,
            match="The requested MSISDNs have different MSISDN "
            "Type or belong to different SIM Profile.",
        ):
            self.simcard.update_sim_card_details(
                imsi_list=imsi_list,
                sim_profile=sim_profile,
                total_records=1,
                msisdn_factor=model.MSISDNFactor.INTERNATIONAL,
                client_ip=client_ip,
                uploaded_by=uploaded_by,
                msisdn_list=msisdn_list,
                valid_data=valid_data,
            )

    def test_update_sim_card_bulk_update_no_such_imsi(self):
        imsi_list = ["**********12345"]
        msisdn_list = ["**********"]
        sim_profile = model.SimProfile.DATA_ONLY
        client_ip = "***********"
        uploaded_by = "admin"
        valid_data = [
            {
                "IMSI": "**********12345",
                "MSISDN": "**********",
            },
            {
                "IMSI": "**********12346",
                "MSISDN": "9876543211",
            },
        ]

        mock_sim_card = MagicMock()
        mock_sim_card.imsi = imsi_list[0]
        mock_sim_card.msisdn = msisdn_list[0]
        mock_sim_card.sim_profile = sim_profile
        mock_sim_card.allocation_id = None

        self.simcard.sim_repository.validate_msisdn_update_request = MagicMock(
            return_value=True
        )
        self.simcard.sim_repository.get_sim_cards.return_value = [mock_sim_card]
        self.simcard.sim_repository.validate_bulk_sim_card_details = MagicMock(
            return_value=[]
        )
        self.simcard.sim_repository.bulk_update_sim_card_details = MagicMock(
            return_value=False
        )

        with pytest.raises(ValueError, match="The dictionary is empty."):
            self.simcard.update_sim_card_details(
                imsi_list=imsi_list,
                sim_profile=sim_profile,
                total_records=1,
                msisdn_factor=model.MSISDNFactor.INTERNATIONAL,
                client_ip=client_ip,
                uploaded_by=uploaded_by,
                msisdn_list=msisdn_list,
                valid_data=valid_data,
            )

    def test_update_sim_card_bulk_update_different_msisdn(self):
        imsi_list = ["**********12345", "**********12346"]
        msisdn_list = ["**********", "9876543211"]
        sim_profile = model.SimProfile.DATA_ONLY
        client_ip = "***********"
        uploaded_by = "admin"
        valid_data = [
            {
                "IMSI": "**********12345",
                "MSISDN": "**********",
            },
            {
                "IMSI": "**********12346",
                "MSISDN": "9876543211",
            },
        ]

        mock_sim_card = MagicMock()
        mock_sim_card.imsi = imsi_list[0]
        mock_sim_card.msisdn = msisdn_list[0]
        mock_sim_card.sim_profile = sim_profile
        mock_sim_card.allocation_id = None
        self.simcard.sim_repository.get_msisdn_factor = MagicMock(
            return_value=["**********", "**********1"]
        )

        self.simcard.sim_repository.validate_msisdn_update_request = MagicMock(
            return_value=True
        )
        self.simcard.sim_repository.get_sim_cards.return_value = [mock_sim_card]
        self.simcard.sim_repository.validate_bulk_sim_card_details = MagicMock(
            return_value=[
                model.SimCardData(
                    requested_imsi=imsi_list[0],
                    requested_msisdn=msisdn_list[0],
                    allocation_id=1,
                    existing_msisdn="**********",
                    msisdn_value=msisdn_list[0],
                    msisdn_sim_profile=sim_profile,
                    msisdn_factor="NATIONAL",
                    existing_imsi=imsi_list[0],
                )
            ]
        )
        self.simcard.sim_repository.bulk_update_sim_card_details = MagicMock(
            return_value=True
        )

        response = self.simcard.update_sim_card_details(
            imsi_list=imsi_list,
            sim_profile=sim_profile,
            total_records=2,
            msisdn_factor=model.MSISDNFactor.INTERNATIONAL,
            client_ip=client_ip,
            uploaded_by=uploaded_by,
            msisdn_list=msisdn_list,
            valid_data=valid_data,
        )

        assert response.total_details == 2

    def test_update_sim_card_bulk_update_active_sim(self):
        imsi_list = ["**********12345", "**********12346"]
        msisdn_list = ["**********", "9876543211"]
        sim_profile = model.SimProfile.DATA_ONLY
        client_ip = "***********"
        uploaded_by = "admin"
        valid_data = [
            {
                "IMSI": "**********12345",
                "MSISDN": "**********",
            },
            {
                "IMSI": "**********12346",
                "MSISDN": "9876543211",
            },
        ]

        mock_sim_card = MagicMock()
        mock_sim_card.imsi = imsi_list[0]
        mock_sim_card.msisdn = msisdn_list[0]
        mock_sim_card.sim_profile = sim_profile
        mock_sim_card.sim_status = SimStatus.ACTIVE
        mock_sim_card.allocation_id = None
        self.simcard.sim_repository.get_msisdn_factor = MagicMock(
            return_value=["**********", "**********1"]
        )

        self.simcard.sim_repository.validate_msisdn_update_request = MagicMock(
            return_value=True
        )
        self.simcard.sim_repository.get_sim_cards.return_value = [mock_sim_card]
        self.simcard.sim_repository.validate_bulk_sim_card_details = MagicMock(
            return_value=[
                model.SimCardData(
                    requested_imsi=imsi_list[0],
                    requested_msisdn=msisdn_list[0],
                    allocation_id=1,
                    existing_msisdn="**********",
                    msisdn_value=msisdn_list[0],
                    msisdn_sim_profile=sim_profile,
                    msisdn_factor="NATIONAL",
                    existing_imsi=imsi_list[0],
                )
            ]
        )
        self.simcard.sim_repository.bulk_update_sim_card_details = MagicMock(
            return_value=True
        )

        response = self.simcard.update_sim_card_details(
            imsi_list=imsi_list,
            sim_profile=sim_profile,
            total_records=2,
            msisdn_factor=model.MSISDNFactor.INTERNATIONAL,
            client_ip=client_ip,
            uploaded_by=uploaded_by,
            msisdn_list=msisdn_list,
            valid_data=valid_data,
        )

        assert response.total_details == 2

    def test_unallocate_sim_cards_success(self):
        mock_imsis = model.Unallocation(imsis=["**********12345", "**********12346"])
        self.simcard.sim_repository.unallocate_sim_cards = MagicMock(
            return_value=model.UnallocateSimCardDetails(
                "2 out of 2 IMSIs were unallocated"
            )
        )
        result = self.simcard.unallocate_sim_cards(imsi_list=mock_imsis)
        assert isinstance(result, model.UnallocateSimCardDetails)

    def test_unallocation_with_not_found_imsis(self):
        mock_imsis = model.Unallocation(imsis=["**********12345", "**********12346"])
        self.simcard.sim_repository.unallocate_sim_cards = MagicMock(
            return_value=model.UnallocateSimCardDetails(
                "0 out of 2 IMSIs were unallocated"
            )
        )
        result = self.simcard.unallocate_sim_cards(imsi_list=mock_imsis)
        assert result.message == "0 out of 2 IMSIs were unallocated"

    def test_imsis_to_delete_success(self):
        trace_id_var.set("12354")

        imsi_list = [600000000000001, 600000000000002, 600000000000003]
        self.simcard.sim_repository.imsis_to_delete = MagicMock(
            return_value={"deleted_imsis_count": 2, "not_found_imsis_count": 1}
        )

        response = self.simcard.imsis_to_delete(imsi_list)

        assert isinstance(response, model.IMSIDeleteResponse)
        assert response.message == "2 out of 3 IMSIs were deleted."

    def test_imsis_to_delete_raises_exception(self):
        trace_id_var.set("12356")

        imsi_list = [600000000000001, 600000000000002]
        error_message = "Database error"

        self.simcard.sim_repository.imsis_to_delete = MagicMock(
            side_effect=Exception(error_message)
        )

        with pytest.raises(Exception) as exc_info:
            self.simcard.imsis_to_delete(imsi_list)

        assert error_message in str(exc_info.value)

    def test_create_order_success(self):
        """Test successful order creation"""
        order_request = domain_model.OrderRequest(
            customer_details=domain_model.OrderCustomer(
                customer_name="Test Customer",
                customer_account_name="Test Account",
                customer_account_id=1,
                customer_email="<EMAIL>",
                customer_contact_no="**********",
            ),
            shipping_details=domain_model.OrderShippingDetails(
                contact_name="John Doe",
                address_line1="123 Test Street",
                address_line2="Suite 100",
                city="Test City",
                state_or_region="Test State",
                postal_code="12345",
                country="Test Country",
            ),
            order_items=[
                domain_model.OrderItem(sim_type="2FF (Mini) SIMs", quantity=100),
                domain_model.OrderItem(sim_type="3FF (Micro) SIMs", quantity=50),
            ],
            order_status_history=domain_model.OrderStatusHistory(
                status_name="PENDING", status_date=datetime.now()
            ),
            notes="Test order notes",
        )

        expected_response = domain_model.OrderResponse(
            order_uuid=uuid4(), message="Order created successfully"
        )

        self.simcard.sim_repository.create_order = MagicMock(
            return_value=expected_response
        )

        result = self.simcard.create_order(order_request)

        assert isinstance(result, domain_model.OrderResponse)
        assert result.message == "Order created successfully"
        assert result.order_uuid is not None
        self.simcard.sim_repository.create_order.assert_called_once_with(
            order=order_request
        )

    def test_create_order_with_minimal_data(self):
        """Test order creation with minimal required data"""
        order_request = domain_model.OrderRequest(
            customer_details=domain_model.OrderCustomer(
                customer_email="<EMAIL>",
                customer_contact_no="**********",
                customer_name="Minimal Customer",
                customer_account_name="Minimal Account",
                customer_account_id=1
            ),
            shipping_details=domain_model.OrderShippingDetails(
                contact_name="Jane Doe",
                address_line1="456 Minimal Street",
                address_line2="",
                city="Minimal City",
                state_or_region="Minimal State",
                postal_code="54321",
                country="Minimal Country"
            ),
            order_items=[
                domain_model.OrderItem(sim_type="4FF (Nano) SIMs", quantity=25)
            ],
            order_status_history=domain_model.OrderStatusHistory(),
        )

        expected_response = domain_model.OrderResponse(
            order_uuid=uuid4(), message="Order created successfully"
        )

        self.simcard.sim_repository.create_order = MagicMock(
            return_value=expected_response
        )

        result = self.simcard.create_order(order_request)

        assert isinstance(result, domain_model.OrderResponse)
        assert result.message == "Order created successfully"
        self.simcard.sim_repository.create_order.assert_called_once()

    def test_create_order_empty_order_items(self):
        """Test order creation with empty order items list"""
        order_request = domain_model.OrderRequest(
            customer_details=domain_model.OrderCustomer(
                customer_email="<EMAIL>",
                customer_contact_no="**********",
                customer_name="Empty Items Customer",
                customer_account_name="Empty Items Account",
                customer_account_id=1
            ),
            shipping_details=domain_model.OrderShippingDetails(
                contact_name="Empty Items Contact",
                address_line1="Empty Items Street",
                address_line2="",
                city="Empty Items City",
                state_or_region="Empty Items State",
                postal_code="33333",
                country="Empty Items Country"
            ),
            order_items=[],
            order_status_history=domain_model.OrderStatusHistory(),
        )

        expected_response = domain_model.OrderResponse(
            order_uuid=uuid4(), message="Order created successfully"
        )

        self.simcard.sim_repository.create_order = MagicMock(
            return_value=expected_response
        )

        result = self.simcard.create_order(order_request)

        assert isinstance(result, domain_model.OrderResponse)
        self.simcard.sim_repository.create_order.assert_called_once()

    def test_create_order_repository_exception(self):
        """Test order creation when repository raises an exception"""
        order_request = domain_model.OrderRequest(
            customer_details=domain_model.OrderCustomer(
                customer_email="<EMAIL>",
                customer_contact_no="**********",
                customer_name="Exception Customer",
                customer_account_name="Exception Account",
                customer_account_id=1
            ),
            shipping_details=domain_model.OrderShippingDetails(
                contact_name="Exception Contact",
                address_line1="Exception Street",
                address_line2="",
                city="Exception City",
                state_or_region="Exception State",
                postal_code="22222",
                country="Exception Country"
            ),
            order_items=[
                domain_model.OrderItem(sim_type="2FF (Mini) SIMs", quantity=100)
            ],
            order_status_history=domain_model.OrderStatusHistory(),
        )

        self.simcard.sim_repository.create_order = MagicMock(
            side_effect=Exception("Database connection error")
        )

        with pytest.raises(Exception) as exc_info:
            self.simcard.create_order(order_request)

        assert "Database connection error" in str(exc_info.value)
        self.simcard.sim_repository.create_order.assert_called_once()

    def test_create_order_multiple_order_items(self):
        """Test order creation with multiple order items"""
        order_request = domain_model.OrderRequest(
            customer_details=domain_model.OrderCustomer(
                customer_email="<EMAIL>",
                customer_contact_no="**********",
                customer_name="Multi Items Customer",
                customer_account_name="Multi Items Account",
                customer_account_id=1
            ),
            shipping_details=domain_model.OrderShippingDetails(
                contact_name="Multi Items Contact",
                address_line1="Multi Items Street",
                address_line2="",
                city="Multi Items City",
                state_or_region="Multi Items State",
                postal_code="44444",
                country="Multi Items Country"
            ),
            order_items=[
                domain_model.OrderItem(sim_type="2FF (Mini) SIMs", quantity=500),
                domain_model.OrderItem(sim_type="3FF (Micro) SIMs", quantity=300),
                domain_model.OrderItem(sim_type="4FF (Nano) SIMs", quantity=200),
            ],
            order_status_history=domain_model.OrderStatusHistory(),
        )

        expected_response = domain_model.OrderResponse(
            order_uuid=uuid4(), message="Order created successfully"
        )

        self.simcard.sim_repository.create_order = MagicMock(
            return_value=expected_response
        )

        result = self.simcard.create_order(order_request)

        assert isinstance(result, domain_model.OrderResponse)
        assert len(order_request.order_items) == 3
        assert order_request.customer_details.customer_name == "Multi Items Customer"
        self.simcard.sim_repository.create_order.assert_called_once()

    def test_create_order_with_notes(self):
        """Test order creation with notes"""
        order_request = domain_model.OrderRequest(
            customer_details=domain_model.OrderCustomer(
                customer_email="<EMAIL>",
                customer_contact_no="**********",
                customer_name="Notes Customer",
                customer_account_name="Notes Account",
                customer_account_id=1
            ),
            shipping_details=domain_model.OrderShippingDetails(
                contact_name="Notes Contact",
                address_line1="Notes Street",
                address_line2="",
                city="Notes City",
                state_or_region="Notes State",
                postal_code="66666",
                country="Notes Country"
            ),
            order_items=[
                domain_model.OrderItem(sim_type="2FF (Mini) SIMs", quantity=100)
            ],
            order_status_history=domain_model.OrderStatusHistory(),
            notes="Special handling required for this order",
        )

        expected_response = domain_model.OrderResponse(
            order_uuid=uuid4(), message="Order created successfully"
        )

        self.simcard.sim_repository.create_order = MagicMock(
            return_value=expected_response
        )

        result = self.simcard.create_order(order_request)

        assert isinstance(result, domain_model.OrderResponse)
        assert order_request.notes == "Special handling required for this order"
        self.simcard.sim_repository.create_order.assert_called_once()


class TestRatePlanChangeSimValidation:
    def setup_method(self):
        self.sim_service = SimService(
            sim_repository=MagicMock(),
            rate_plan_repository=MagicMock(),
            provisioning=MagicMock(),
            cdr_repository=MagicMock(),
            market_share_api=MagicMock(),
            audit_service=MagicMock(),
            media_service=MagicMock(),
            redis_api=MagicMock(),
        )

    def test_rate_plan_not_found(self):
        """Test when the rate plan does not exist"""
        self.sim_service.sim_repository.get_rate_plan_with_allocation_count = MagicMock(
            return_value=None
        )

        with pytest.raises(exceptions.RatePlanNotFound) as exc_info:
            self.sim_service.rate_plan_change_sim_validation(
                imsi="************000", account_id=1, rate_plan_id=10
            )

        assert str(exc_info.value) == "Rate Plan with id: 10 does not exist."

    def test_rate_plan_account_mismatch(self):
        """Test when the rate plan does not belong to the given account"""
        self.sim_service.sim_repository.get_rate_plan_with_allocation_count = MagicMock(
            return_value=MagicMock(account_id=99)
        )

        with pytest.raises(exceptions.RatePlanAccountMappingError) as exc_info:
            self.sim_service.rate_plan_change_sim_validation(
                imsi="************000", account_id=1, rate_plan_id=10
            )

        assert str(exc_info.value) == "Incorrect account for the specified Rate Plan."

    def test_sim_not_found(self):
        """Test when the SIM is not found"""
        self.sim_service.sim_repository.get_rate_plan_with_allocation_count = MagicMock(
            return_value=MagicMock(account_id=1)
        )
        self.sim_service.sim_repository.get_rate_plan_by_imsi = MagicMock(
            return_value=None
        )

        with pytest.raises(exceptions.SimCardsNotFound) as exc_info:
            self.sim_service.rate_plan_change_sim_validation(
                imsi="************000", account_id=1, rate_plan_id=10
            )

        assert str(exc_info.value) == "SIM not found."

    def test_imsi_already_assigned_to_plan(self):
        """Test when the IMSI is already assigned to the requested rate plan"""
        self.sim_service.sim_repository.get_rate_plan_with_allocation_count = MagicMock(
            return_value=MagicMock(account_id=1)
        )
        self.sim_service.sim_repository.get_rate_plan_by_imsi = MagicMock(
            return_value=10
        )

        with pytest.raises(exceptions.RatePlanException) as exc_info:
            self.sim_service.rate_plan_change_sim_validation(
                imsi="************000", account_id=1, rate_plan_id=10
            )

        assert str(exc_info.value) == "IMSI already assigned to the specified plan."

    def test_rate_plan_change_not_allowed(self):
        """Test when the rate plan has reached its SIM allocation limit"""
        self.sim_service.sim_repository.get_rate_plan_with_allocation_count = MagicMock(
            return_value=MagicMock(account_id=1, sim_limit=5, allocated_sim_count=5)
        )
        self.sim_service.sim_repository.get_rate_plan_by_imsi = MagicMock(
            return_value=20
        )

        with pytest.raises(exceptions.RatePlanChangeNotAllowed) as exc_info:
            self.sim_service.rate_plan_change_sim_validation(
                imsi="************000", account_id=1, rate_plan_id=10
            )

        assert str(exc_info.value) == "Rate Plan can't be changed"

    def test_successful_rate_plan_change(self):
        """Test successful rate plan change when limits allow"""
        self.sim_service.sim_repository.get_rate_plan_with_allocation_count = MagicMock(
            return_value=MagicMock(account_id=1, sim_limit=10, allocated_sim_count=5)
        )
        self.sim_service.sim_repository.get_rate_plan_by_imsi = MagicMock(
            return_value=20
        )

        result = self.sim_service.rate_plan_change_sim_validation(
            imsi="************000", account_id=1, rate_plan_id=10
        )

        assert isinstance(result, model.RatePlanChangeSimLimitResult)
        assert result.message == "Rate Plan will be changed"


class TestSimNotification:
    def setup_method(self):
        self.sim_notification = SimNotification(
            AbstractSimRepository,
            AbstractAuditService,
        )

    def test_process_notification_when_exist(self, make_sim_provider_log):
        work_id = "c19d2f7d-d686-472a-ac00-fdb858546b82"
        status = "SUCCESS"
        audit_date = datetime.today()
        client_ip = "127.0.0.1"
        sim_provider_log = make_sim_provider_log(work_id, status, audit_date)

        self.sim_notification.audit_service.validate_notification = MagicMock(
            return_value=False
        )
        result = self.sim_notification.process_notification(
            RequestType.CEASE, sim_provider_log, client_ip
        )
        assert result
        self.sim_notification.audit_service.validate_notification.assert_called_once()

    # fmt: off
    def test_process_notification_when_work_item_id_not_exist(
        self, make_sim_provider_log
    ):
        work_id = "dc01d2b1-a7ba-41da-a00d-80598b7e2b7d"
        status = "SUCCESS"
        audit_date = datetime.today()
        client_ip = "127.0.0.1"
        sim_provider_log = make_sim_provider_log(work_id, status, audit_date)
        expected_message = f"Work item id not found - {sim_provider_log.work_id}"
        self.sim_notification.audit_service.validate_notification = MagicMock(
            return_value=True
        )
        self.sim_notification.audit_service.get_provider_log_info = MagicMock(
            return_value=None
        )
        self.sim_notification.audit_service.get_sim_provider_log_audit_api = MagicMock(
            return_value=None
        )
        self.sim_notification.audit_service.add_sim_provider_audit_api = MagicMock(
            return_value=None
        )

        with pytest.raises(exceptions.WorkItemIdNotFound) as e:
            self.sim_notification.process_notification(
                RequestType.CEASE, sim_provider_log, client_ip
            )
        assert str(e.value) == expected_message
        (
            self.sim_notification.audit_service
            .get_sim_provider_log_audit_api.assert_called_once()
        )

    # fmt: off
    def test_process_notification(self, make_sim_provider_log):
        work_id = "dc01d2b1-a7ba-41da-a00d-80598b7e2b7d"
        status = "SUCCESS"
        audit_date = datetime.today()
        client_ip = "127.0.0.1"
        sim_provider_log = make_sim_provider_log(work_id, status, audit_date)
        self.sim_notification.sim_repository.validate_notification = MagicMock(
            return_value=True
        )
        self.sim_notification.sim_repository.get_provider_log_info = MagicMock(
            return_value=sim_provider_log
        )

        self.sim_notification.audit_service.get_sim_provider_log_audit_api = MagicMock(
            return_value=Providerlog(**PROVIDER_LOG)
        )
        self.sim_notification.audit_service.add_sim_provider_audit_api = MagicMock(
            return_value=None
        )
        self.sim_notification.update_sim_card_based_on_response = MagicMock(
            return_value=True
        )
        result = self.sim_notification.process_notification(
            RequestType.CEASE, sim_provider_log, client_ip
        )
        expected_response = True
        assert result == expected_response
        assert isinstance(result, bool)
        (
            self.sim_notification.audit_service
            .get_sim_provider_log_audit_api.assert_called_once()
        )
        (
            self.sim_notification.audit_service
            .add_sim_provider_audit_api.assert_called_once()
        )

    def test_update_sim_card_based_on_response_status_success(
        self, make_sim_provider_log
    ):
        work_id = "dc01d2b1-a7ba-41da-a00d-80598b7e2b7d"
        status = "SUCCESS"
        audit_date = datetime.today()
        sim_activity = ProviderLog(
            imsi=IMSI("************000"),
            requestType="CEASE",
            priorStatus=SimStatus.ACTIVE,
        )
        sim_provider_log = make_sim_provider_log(work_id, status, audit_date)
        self.sim_notification.sim_repository.update_sim_card = MagicMock(
            return_value=None
        )
        self.sim_notification.get_sim_card = MagicMock(
            return_value=model.SIMCard(
                iccid=ICCID("8986000000000000001"),
                msisdn=MSISDN("************000"),
                imsi=IMSI("************000"),
            )
        )
        self.sim_notification.create_sim_activity_log = MagicMock(return_value=None)
        self.sim_notification._add_sim_monthly_status = MagicMock(return_value=None)
        self.sim_notification._get_sim_monthly_status = MagicMock(
            return_value=model.SIMMonthlyStatus(
                sim_card_id=1,
                month=Month(year=2023, month=5, day=1),
                sim_status=SimStatus.DEACTIVATED,
                is_first_activation=True,
            )
        )
        result = self.sim_notification.update_sim_card_based_on_response(
            sim_provider_log,
            sim_activity,
            SimStatus.DEACTIVATED,
            NotificationStatus.SUCCESS,
            RequestType.CEASE,
        )
        assert result is True

    def test_update_sim_card_based_on_response_status_failure(
        self, make_sim_provider_log
    ):
        work_id = "dc01d2b1-a7ba-41da-a00d-80598b7e2b7d"
        status = "SUCCESS"
        audit_date = datetime.today()
        provider_log = ProviderLog(
            imsi=IMSI("************000"),
            requestType="CEASE",
            priorStatus=SimStatus.ACTIVE,
        )
        sim_provider_log = make_sim_provider_log(work_id, status, audit_date)
        self.sim_notification.sim_repository.update_sim_card = MagicMock(
            return_value=None
        )
        self.sim_notification.get_sim_card = MagicMock(
            return_value=model.SIMCard(
                iccid=ICCID("8986000000000000001"),
                msisdn=MSISDN("************000"),
                imsi=IMSI("************000"),
            )
        )
        self.sim_notification.create_sim_activity_log = MagicMock(return_value=None)
        self.sim_notification.audit_service.add_sim_audit_api = MagicMock(
            return_value=None
        )
        result = self.sim_notification.update_sim_card_based_on_response(
            sim_provider_log,
            provider_log,
            SimStatus.DEACTIVATED,
            NotificationStatus.FAILURE,
            RequestType.CEASE,
        )
        assert result is True

    def test_get_sim_card(self):
        self.sim_notification.sim_repository.get_sim_cards = MagicMock(
            return_value=iter(
                [
                    model.SIMCard(
                        iccid=ICCID("8986000000000000001"),
                        msisdn=MSISDN("************000"),
                        imsi=IMSI("************000"),
                        allocation_id=1,
                    )
                ]
            )
        )
        result = self.sim_notification.get_sim_card(imsi=IMSI("************000"))
        assert isinstance(result, model.SIMCard)

    def test_get_sim_card_not_found(self):
        self.sim_notification.sim_repository.get_sim_card = MagicMock(
            return_value=iter([])
        )
        with pytest.raises(exceptions.SimCardsNotFound) as e:
            self.sim_notification.get_sim_card(imsi=IMSI("************000"))
        assert str(e.value) == "Requested IMSI not found."

    def test_get_sim_card_allocation_not_found(self):
        self.sim_notification.sim_repository.get_sim_cards = MagicMock(
            return_value=iter(
                [
                    model.SIMCard(
                        iccid=ICCID("8986000000000000001"),
                        msisdn=MSISDN("************000"),
                        imsi=IMSI("************000"),
                        allocation_id=None,
                    )
                ]
            )
        )
        with pytest.raises(exceptions.AllocationError) as e:
            self.sim_notification.get_sim_card(imsi=IMSI("************000"))
        assert str(e.value) == "IMSI allocation not found."

    def test__add_sim_monthly_status_sim_fee_not_applicable(self):
        sim_monthly_status = model.SIMMonthlyStatus(
            sim_card_id=1,
            month=Month(year=2020, month=1, day=1),
            sim_status=SimStatus.ACTIVE,
            is_first_activation=True,
        )
        self.sim_notification.sim_repository.check_sim_monthly_status = MagicMock(
            return_value=False
        )
        self.sim_notification.sim_repository.add_sim_monthly_status = MagicMock(
            return_value=None
        )
        result = self.sim_notification._add_sim_monthly_status(
            sim_monthly_status, SimStatus.ACTIVE
        )
        assert result is None

    def test__add_sim_monthly_status_sim_fee_applicable(self):
        sim_monthly_status = model.SIMMonthlyStatus(
            sim_card_id=1,
            month=Month(year=2020, month=1, day=1),
            sim_status=SimStatus.ACTIVE,
            is_first_activation=True,
        )
        self.sim_notification.sim_repository.check_sim_monthly_status = MagicMock(
            return_value=True
        )
        self.sim_notification.sim_repository.add_sim_monthly_status = MagicMock(
            return_value=None
        )
        self.sim_notification.sim_repository.update_sim_monthly_status = MagicMock(
            return_value=None
        )
        result = self.sim_notification._add_sim_monthly_status(
            sim_monthly_status, SimStatus.ACTIVE
        )
        assert result is None

    def test__get_sim_monthly_status(self):
        self.sim_notification._get_sim_card = MagicMock(
            return_value=model.SIMCard(
                id=1,
                iccid=ICCID("8986000000000000001"),
                msisdn=MSISDN("************000"),
                imsi=IMSI("************000"),
                allocation_id=1,
            )
        )
        sim_provider_log = model.SIMProviderLog(
            sim_activity_log_uuid="b93a6553-ddf2-4ce4-9a62-e9c588cf4524",
            activity_id="ea4c11bc-afca-4d91-be8b-a77e4829964a",
            audit_date=datetime.now(),
            message="test",
            status="Active",
            work_id="test",
            prior_status=SimStatus.ACTIVE,
        )
        result = self.sim_notification._get_sim_monthly_status(
            "************000", sim_provider_log
        )
        assert isinstance(result, model.SIMMonthlyStatus)

    def test__get_sim_card(self):
        self.sim_notification.sim_repository.get_sim_cards = MagicMock(
            return_value=iter(
                [
                    model.SIMCard(
                        iccid=ICCID("8986000000000000001"),
                        msisdn=MSISDN("************000"),
                        imsi=IMSI("************000"),
                        allocation_id=1,
                    )
                ]
            )
        )
        result = self.sim_notification._get_sim_card(imsi=IMSI("************000"))
        assert isinstance(result, model.SIMCard)

    def test__get_sim_card_not_found(self):
        self.sim_notification.sim_repository.get_sim_card = MagicMock(
            return_value=iter([])
        )
        with pytest.raises(exceptions.SimCardsNotFound) as e:
            self.sim_notification._get_sim_card(imsi=IMSI("************000"))
        assert str(e.value) == "Requested reference not found."
