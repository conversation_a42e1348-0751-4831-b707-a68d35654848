"""database tables for ordering process

Revision ID: 7b2811f9afc0
Revises: 0cf658516c83
Create Date: 2025-05-21 17:44:42.448796

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects.postgresql import UUID

# revision identifiers, used by Alembic.
revision = "7b2811f9afc0"
down_revision = "0cf658516c83"
branch_labels = None
depends_on = None


def upgrade():

    # orders
    op.create_table(
        "orders",
        sa.Column("id", sa.Integer, primary_key=True),
        sa.Column(
            "uuid",
            UUID(as_uuid=True),
            nullable=False,
        ),
        sa.Column("notes", sa.Text),
        sa.Column("order_date", sa.TIMESTAMP, server_default=sa.func.now()),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("uuid"),
    )

    # customers
    op.create_table(
        "order_customers",
        sa.Column("id", sa.Integer, primary_key=True),
        sa.Column(
            "uuid",
            UUID(as_uuid=True),
        ),
        sa.Column("customer_name", sa.String(length=255)),
        sa.Column("customer_account_name", sa.String(length=255)),
        sa.Column("customer_account_id", sa.Integer),
        sa.Column("customer_account_logo_url", sa.String(length=255)),
        sa.Column("customer_email", sa.String(length=255)),
        sa.Column("customer_contact_no", sa.String(length=50)),
        sa.Column("order_id", UUID, sa.ForeignKey("orders.uuid")),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("uuid"),
    )

    # shipping_details
    op.create_table(
        "order_shipping_details",
        sa.Column("id", sa.Integer, primary_key=True, autoincrement=True),
        sa.Column("uuid", UUID(as_uuid=True)),
        sa.Column("contact_name", sa.String(length=255)),
        sa.Column("address_line1", sa.String(length=255)),
        sa.Column("address_line2", sa.String(length=255)),
        sa.Column("city", sa.String(length=100)),
        sa.Column("state_or_region", sa.String(length=100)),
        sa.Column("postal_code", sa.String(length=20)),
        sa.Column("country", sa.String(length=100)),
        sa.Column("other_information", sa.Text),
        sa.Column("order_id", UUID, sa.ForeignKey("orders.uuid")),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("uuid"),
    )

    # order_items
    op.create_table(
        "order_items",
        sa.Column("id", sa.Integer, primary_key=True, autoincrement=True),
        sa.Column("uuid", UUID(as_uuid=True)),
        sa.Column("order_id", UUID, sa.ForeignKey("orders.uuid")),
        sa.Column("sim_type", sa.String(length=100)),
        sa.Column("quantity", sa.Integer),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("uuid"),
    )

    # order_status_history
    op.create_table(
        "order_status_history",
        sa.Column("id", sa.Integer, primary_key=True, autoincrement=True),
        sa.Column("uuid", UUID(as_uuid=True)),
        sa.Column("status_name", sa.String(length=50)),
        sa.Column("status_date", sa.TIMESTAMP, server_default=sa.func.now()),
        sa.Column("order_id", UUID, sa.ForeignKey("orders.uuid")),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("uuid"),
    )

    # orders_tracking
    op.create_table(
        "order_tracking",
        sa.Column("id", sa.Integer, primary_key=True),
        sa.Column("uuid", UUID(as_uuid=True)),
        sa.Column("reference_id", sa.String(length=100), unique=True),
        sa.Column("reference_url", sa.String(length=100), unique=True),
        sa.Column("order_id", UUID, sa.ForeignKey("orders.uuid")),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("uuid"),
    )


def downgrade():
    op.drop_table("order_tracking")
    op.drop_table("order_status_history")
    op.drop_table("order_items")
    op.drop_table("order_shipping_details")
    op.drop_table("order_customers")
    op.drop_table("orders")
